<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.ams.material.dao.InbDtlMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.ams.material.model.InbDtl">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="inb_id" jdbcType="VARCHAR" property="inbId" />
    <result column="batch_no" jdbcType="VARCHAR" property="batchNo" />
    <result column="sku_id" jdbcType="VARCHAR" property="skuId" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="num" jdbcType="INTEGER" property="num" />
    <result column="total_amt" jdbcType="DECIMAL" property="totalAmt" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="dept_id" jdbcType="VARCHAR" property="deptId" />
    <result column="dept_name" jdbcType="VARCHAR" property="deptName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode" />
    <result column="sso_org_name" jdbcType="VARCHAR" property="ssoOrgName" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
  <insert id="batchInsert" parameterType="java.util.List">
    INSERT INTO m_inb_dtl
    (
      id,
      inb_id,
      batch_no,
      sku_id,
      price,
      num,
      total_amt,
      create_date,
      create_user,
      create_user_name,
      dept_id,
      dept_name,
      update_date,
      update_user,
      update_user_name,
      sso_org_code,
      sso_org_name,
      is_deleted,
      remark
    )
    VALUES
    <foreach collection="inbDtlList" item="item" index="index" separator=",">
    (
      #{item.id,jdbcType=VARCHAR},
      #{item.inbId,jdbcType=VARCHAR},
      #{item.batchNo,jdbcType=VARCHAR},
      #{item.skuId,jdbcType=VARCHAR},
      #{item.price,jdbcType=DECIMAL},
      #{item.num,jdbcType=INTEGER},
      #{item.totalAmt,jdbcType=DECIMAL},
      #{item.createDate,jdbcType=TIMESTAMP},
      #{item.createUser,jdbcType=VARCHAR},
      #{item.createUserName,jdbcType=VARCHAR},
      #{item.deptId,jdbcType=VARCHAR},
      #{item.deptName,jdbcType=VARCHAR},
      #{item.updateDate,jdbcType=TIMESTAMP},
      #{item.updateUser,jdbcType=VARCHAR},
      #{item.updateUserName,jdbcType=VARCHAR},
      #{item.ssoOrgCode,jdbcType=VARCHAR},
      #{item.ssoOrgName,jdbcType=VARCHAR},
      #{item.isDeleted,jdbcType=CHAR},
      #{item.remark,jdbcType=VARCHAR}
    )
    </foreach>
  </insert>
  <select id="getInbDtlExtListByInbId" resultType="cn.trasen.ams.material.bean.inb.InbDtlResp" parameterType="cn.trasen.ams.material.bean.inb.InbDtlResp">
    SELECT
        t1.*,
        t2.flow_no,
        t2.name,
        t2.model,
        t2.unit,
        t2.price as origin_price,
        t2.reg_no,
        t2.brand,
        t3.name as category_name,
        t4.name as manufacturer_name,
        t5.prod_no,
        t5.prod_date,
        t5.expire_date
    FROM m_inb_dtl t1
    LEFT JOIN m_sku t2 ON t1.sku_id = t2.id AND t2.is_deleted = 'N'
    LEFT JOIN c_category t3 ON t2.category_id = t3.id AND t3.is_deleted = 'N'
    LEFT JOIN c_manufacturer t4 ON t2.manufacturer_id = t4.id AND t4.is_deleted = 'N'
    LEFT JOIN m_batch t5 ON t1.batch_no = t5.batch_no AND t5.is_deleted = 'N'
    WHERE t1.inb_id = #{inbId}
      AND t1.is_deleted = 'N'
    ORDER BY t1.create_date ASC
  </select>
</mapper>