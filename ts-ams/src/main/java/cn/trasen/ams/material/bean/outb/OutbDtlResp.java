package cn.trasen.ams.material.bean.outb;

import cn.trasen.ams.material.model.OutbDtl;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.material.bean.outb
 * @className: OutbDtlResp
 * @author: chenbin
 * @description: 物资出库单明细扩展响应体
 * @date: 2025/8/1 16:30
 * @version: 1.0
 */
@Data
public class OutbDtlResp extends OutbDtl {
    @ApiModelProperty(value = "物资编码")
    private String flowNo;

    @ApiModelProperty(value = "物资名称")
    private String name;

    @ApiModelProperty(value = "物资分类名称")
    private String categoryName;

    @ApiModelProperty(value = "规格型号")
    private String model;

    @ApiModelProperty(value = "单位")
    private String unit;

    @ApiModelProperty(value = "单位显示")
    private String unitShow;

    @ApiModelProperty(value = "参考单价")
    private BigDecimal originPrice;

    @ApiModelProperty(value = "注册证编号")
    private String regNo;

    @ApiModelProperty(value = "品牌")
    private String brand;

    @ApiModelProperty(value = "生产厂家名称")
    private String manufacturerName;

    @ApiModelProperty(value = "库存数量")
    private Integer stockNum;

    @ApiModelProperty(value = "可出库数量")
    private Integer availableNum;
}
