<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.ams.material.dao.OutbDtlMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.ams.material.model.OutbDtl">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="outb_id" jdbcType="VARCHAR" property="outbId" />
    <result column="sku_id" jdbcType="VARCHAR" property="skuId" />
    <result column="batch_no" jdbcType="VARCHAR" property="batchNo" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="num" jdbcType="INTEGER" property="num" />
    <result column="total_amt" jdbcType="DECIMAL" property="totalAmt" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="dept_id" jdbcType="VARCHAR" property="deptId" />
    <result column="dept_name" jdbcType="VARCHAR" property="deptName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode" />
    <result column="sso_org_name" jdbcType="VARCHAR" property="ssoOrgName" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>

  <insert id="batchInsert" parameterType="java.util.List">
    INSERT INTO m_outb_dtl (
      id,
      outb_id,
      sku_id,
      batch_no,
      price,
      num,
      total_amt,
      create_date,
      create_user,
      create_user_name,
      dept_id,
      dept_name,
      update_date,
      update_user,
      update_user_name,
      sso_org_code,
      sso_org_name,
      is_deleted
    ) VALUES
    <foreach collection="outbDtlList" item="item" separator=",">
      (
        #{item.id},
        #{item.outbId},
        #{item.skuId},
        #{item.batchNo},
        #{item.price},
        #{item.num},
        #{item.totalAmt},
        #{item.createDate},
        #{item.createUser},
        #{item.createUserName},
        #{item.deptId},
        #{item.deptName},
        #{item.updateDate},
        #{item.updateUser},
        #{item.updateUserName},
        #{item.ssoOrgCode},
        #{item.ssoOrgName},
        #{item.isDeleted}
      )
    </foreach>
  </insert>

  <select id="getOutbDtlExtListByOutbId" resultType="cn.trasen.ams.material.bean.outb.OutbDtlResp" parameterType="cn.trasen.ams.material.bean.outb.OutbDtlResp">
    SELECT
        t1.*,
        t2.flow_no,
        t2.name,
        t2.model,
        t2.unit,
        t2.price as origin_price,
        t2.reg_no,
        t2.brand,
        t3.name as category_name,
        t4.name as manufacturer_name
    FROM m_outb_dtl t1
    LEFT JOIN m_sku t2 ON t1.sku_id = t2.id AND t2.is_deleted = 'N'
    LEFT JOIN c_category t3 ON t2.category_id = t3.id AND t3.is_deleted = 'N'
    LEFT JOIN c_manufacturer t4 ON t2.manufacturer_id = t4.id AND t4.is_deleted = 'N'
    WHERE t1.outb_id = #{outbId}
      AND t1.is_deleted = 'N'
    ORDER BY t1.create_date ASC
  </select>
</mapper>