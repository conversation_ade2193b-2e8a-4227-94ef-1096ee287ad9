package cn.trasen.ams.material.dao;

import cn.trasen.ams.material.bean.inb.InbDtlResp;
import cn.trasen.ams.material.model.InbDtl;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface InbDtlMapper extends Mapper<InbDtl> {
    void batchInsert(@Param("inbDtlList") List<InbDtl> inbDtlList);

    List<InbDtlResp> getInbDtlExtListByInbId(@Param("inbId") String inbId, @Param("name") String name);
}