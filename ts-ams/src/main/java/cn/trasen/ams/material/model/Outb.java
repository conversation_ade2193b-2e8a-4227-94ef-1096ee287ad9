package cn.trasen.ams.material.model;

import io.swagger.annotations.*;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;

import lombok.*;

@Table(name = "m_outb")
@Setter
@Getter
public class Outb {
    /**
     * 主键
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 入库单ID 适用于快捷入出库的
     */
    @Column(name = "inb_id")
    @ApiModelProperty(value = "入库单ID 适用于快捷入出库的")
    private String inbId;

    /**
     * 单据流水号
     */
    @Column(name = "flow_no")
    @ApiModelProperty(value = "单据流水号")
    private String flowNo;

    /**
     * 方式码关系ID
     */
    @Column(name = "mtd_code_id")
    @ApiModelProperty(value = "方式码关系ID")
    private String mtdCodeId;

    /**
     * 出库仓库ID
     */
    @Column(name = "ware_id")
    @ApiModelProperty(value = "出库仓库ID")
    private String wareId;

    /**
     * 出库科室ID
     */
    @Column(name = "out_dept_id")
    @ApiModelProperty(value = "出库科室ID")
    private String outDeptId;

    /**
     * 出库科室名称
     */
    @Column(name = "out_dept_name")
    @ApiModelProperty(value = "出库科室名称")
    private String outDeptName;

    /**
     * 领用人ID
     */
    @Column(name = "apply_user_id")
    @ApiModelProperty(value = "领用人ID")
    private String applyUserId;

    /**
     * 领用人名称
     */
    @Column(name = "apply_user_name")
    @ApiModelProperty(value = "领用人名称")
    private String applyUserName;

    /**
     * 出库总金额
     */
    @Column(name = "total_amt")
    @ApiModelProperty(value = "出库总金额")
    private BigDecimal totalAmt;

    /**
     * 打印状态 0 未打印 1已打印
     */
    @Column(name = "print_stat")
    @ApiModelProperty(value = "打印状态 0 未打印 1已打印")
    private String printStat;

    /**
     * 出库日期
     */
    @Column(name = "out_date")
    @ApiModelProperty(value = "出库日期")
    private Date outDate;

    /**
     * 出库人ID
     */
    @Column(name = "outer_id")
    @ApiModelProperty(value = "出库人ID")
    private String outerId;

    /**
     * 出库人名称
     */
    @Column(name = "outer_name")
    @ApiModelProperty(value = "出库人名称")
    private String outerName;

    /**
     * 审核人ID
     */
    @Column(name = "doer_id")
    @ApiModelProperty(value = "审核人ID")
    private String doerId;

    /**
     * 审核人名称
     */
    @Column(name = "doer_name")
    @ApiModelProperty(value = "审核人名称")
    private String doerName;

    /**
     * 审核时间
     */
    @Column(name = "do_time")
    @ApiModelProperty(value = "审核时间")
    private Date doTime;

    /**
     * 撤销人ID
     */
    @Column(name = "canceler_id")
    @ApiModelProperty(value = "撤销人ID")
    private String cancelerId;

    /**
     * 撤销人名称
     */
    @Column(name = "canceler_name")
    @ApiModelProperty(value = "撤销人名称")
    private String cancelerName;

    /**
     * 撤销时间
     */
    @Column(name = "cancel_time")
    @ApiModelProperty(value = "撤销时间")
    private Date cancelTime;

    /**
     * 状态 1 已登记 2 已确认
     */
    @ApiModelProperty(value = "状态 1 已登记 2 已确认")
    private String stat;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人账号
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人账号")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 部门ID
     */
    @Column(name = "dept_id")
    @ApiModelProperty(value = "部门ID")
    private String deptId;

    /**
     * 部门名称
     */
    @Column(name = "dept_name")
    @ApiModelProperty(value = "部门名称")
    private String deptName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 更新人账号
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人账号")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    /**
     * 机构名称
     */
    @Column(name = "sso_org_name")
    @ApiModelProperty(value = "机构名称")
    private String ssoOrgName;

    /**
     * 是否删除 Y N
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "是否删除 Y N")
    private String isDeleted;
}