<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.ams.material.dao.OutbMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.ams.material.model.Outb">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="inb_id" jdbcType="VARCHAR" property="inbId" />
    <result column="flow_no" jdbcType="VARCHAR" property="flowNo" />
    <result column="mtd_code_id" jdbcType="VARCHAR" property="mtdCodeId" />
    <result column="ware_id" jdbcType="VARCHAR" property="wareId" />
    <result column="out_dept_id" jdbcType="VARCHAR" property="outDeptId" />
    <result column="out_dept_name" jdbcType="VARCHAR" property="outDeptName" />
    <result column="apply_user_id" jdbcType="VARCHAR" property="applyUserId" />
    <result column="apply_user_name" jdbcType="VARCHAR" property="applyUserName" />
    <result column="total_amt" jdbcType="DECIMAL" property="totalAmt" />
    <result column="print_stat" jdbcType="CHAR" property="printStat" />
    <result column="out_date" jdbcType="DATE" property="outDate" />
    <result column="outer_id" jdbcType="VARCHAR" property="outerId" />
    <result column="outer_name" jdbcType="VARCHAR" property="outerName" />
    <result column="doer_id" jdbcType="VARCHAR" property="doerId" />
    <result column="doer_name" jdbcType="VARCHAR" property="doerName" />
    <result column="doer_time" jdbcType="TIMESTAMP" property="doerTime" />
    <result column="stat" jdbcType="VARCHAR" property="stat" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="dept_id" jdbcType="VARCHAR" property="deptId" />
    <result column="dept_name" jdbcType="VARCHAR" property="deptName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode" />
    <result column="sso_org_name" jdbcType="VARCHAR" property="ssoOrgName" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
</mapper>