package cn.trasen.ams.material.service;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.material.model.Outb;

/**
 * @ClassName OutbService
 * @Description TODO
 * @date 2025年8月1日 下午4:43:14
 * <AUTHOR>
 * @version 1.0
 */
public interface OutbService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2025年8月1日 下午4:43:14
	 * <AUTHOR>
	 */
	Integer save(Outb record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2025年8月1日 下午4:43:14
	 * <AUTHOR>
	 */
	Integer update(Outb record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2025年8月1日 下午4:43:14
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return Outb
	 * @date 2025年8月1日 下午4:43:14
	 * <AUTHOR>
	 */
	Outb selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<Outb>
	 * @date 2025年8月1日 下午4:43:14
	 * <AUTHOR>
	 */
	DataSet<Outb> getDataSetList(Page page, Outb record);
}
