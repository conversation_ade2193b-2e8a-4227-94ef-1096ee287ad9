package cn.trasen.ams.material.service;

import cn.trasen.ams.material.bean.outb.OutbInsertReq;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.material.model.Outb;

import java.util.List;

/**
 * @ClassName OutbService
 * @Description TODO
 * @date 2025年8月1日 下午4:43:14
 * <AUTHOR>
 * @version 1.0
 */
public interface OutbService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2025年8月1日 下午4:43:14
	 * <AUTHOR>
	 */
	Integer save(Outb record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2025年8月1日 下午4:43:14
	 * <AUTHOR>
	 */
	Integer update(Outb record);

	/**
	 *
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2025年8月1日 下午4:43:14
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return Outb
	 * @date 2025年8月1日 下午4:43:14
	 * <AUTHOR>
	 */
	Outb selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<Outb>
	 * @date 2025年8月1日 下午4:43:14
	 * <AUTHOR>
	 */
	DataSet<Outb> getDataSetList(Page page, Outb record);

	/**
	 * @Title insert
	 * @Description 新增出库单
	 * @param record
	 * @return void
	 * @date 2025年8月1日 下午4:45:00
	 * <AUTHOR>
	 */
	void insert(OutbInsertReq record);

	/**
	 * @Title edit
	 * @Description 编辑出库单
	 * @param record
	 * @return void
	 * @date 2025年8月1日 下午4:45:00
	 * <AUTHOR>
	 */
	void edit(OutbInsertReq record);

	/**
	 * @Title remove
	 * @Description 删除出库单
	 * @param outbId
	 * @return void
	 * @date 2025年8月1日 下午4:45:00
	 * <AUTHOR>
	 */
	void remove(String outbId);

	/**
	 * @Title batchRemove
	 * @Description 批量删除出库单
	 * @param outbIdList
	 * @return void
	 * @date 2025年8月1日 下午4:45:00
	 * <AUTHOR>
	 */
	void batchRemove(List<String> outbIdList);

	/**
	 * @Title batchConfirm
	 * @Description 批量确认出库单
	 * @param outbIdList
	 * @return void
	 * @date 2025年8月1日 下午4:45:00
	 * <AUTHOR>
	 */
	void batchConfirm(List<String> outbIdList);

	/**
	 * @Title rollbackBatchConfirm
	 * @Description 批量撤销确认出库单
	 * @param outbIdList
	 * @return void
	 * @date 2025年8月1日 下午4:45:00
	 * <AUTHOR>
	 */
	void rollbackBatchConfirm(List<String> outbIdList);

	/**
	 * @Title getTargetOutbId
	 * @Description 获取目标出库单ID（上一条/下一条/当前）
	 * @param currentId
	 * @param direction
	 * @return String
	 * @date 2025年8月1日 下午4:45:00
	 * <AUTHOR>
	 */
	String getTargetOutbId(String currentId, String direction);

	/**
	 * @Title createFromInb
	 * @Description 通过入库单创建出库单
	 * @param inbId
	 * @return OutbInsertReq
	 * @date 2025年8月1日 下午4:45:00
	 * <AUTHOR>
	 */
	OutbInsertReq createFromInb(String inbId);

	/**
	 * @Title dataFmt
	 * @Description 数据格式化
	 * @param record
	 * @return void
	 * @date 2025年8月1日 下午4:45:00
	 * <AUTHOR>
	 */
	void dataFmt(Outb record);
}
