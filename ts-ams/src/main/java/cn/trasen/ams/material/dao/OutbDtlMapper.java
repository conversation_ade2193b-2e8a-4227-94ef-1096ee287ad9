package cn.trasen.ams.material.dao;

import cn.trasen.ams.material.bean.outb.OutbDtlResp;
import cn.trasen.ams.material.model.OutbDtl;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface OutbDtlMapper extends Mapper<OutbDtl> {
    void batchInsert(@Param("outbDtlList") List<OutbDtl> outbDtlList);

    List<OutbDtlResp> getOutbDtlExtListByOutbId(String outbId);
}