package cn.trasen.ams.material.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import cn.trasen.ams.common.constant.CommonConst;
import cn.trasen.ams.common.model.Warehouse;
import cn.trasen.ams.common.service.WarehouseService;
import cn.trasen.ams.material.bean.outb.OutbInsertReq;
import cn.trasen.ams.material.constant.OrdConst;
import cn.trasen.ams.material.model.InbDtl;
import cn.trasen.ams.material.model.MethodCode;
import cn.trasen.ams.material.model.MtdCodeRela;
import cn.trasen.ams.material.model.OutbDtl;
import cn.trasen.ams.material.service.InbDtlService;
import cn.trasen.ams.material.service.InbService;
import cn.trasen.ams.material.service.MethodCodeService;
import cn.trasen.ams.material.service.MtdCodeRelaService;
import cn.trasen.ams.material.service.OutbDtlService;
import com.alibaba.fastjson.JSON;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.ams.material.dao.OutbMapper;
import cn.trasen.ams.material.model.Outb;
import cn.trasen.ams.material.service.OutbService;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName OutbServiceImpl
 * @Description TODO
 * @date 2025年8月1日 下午4:43:14
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class OutbServiceImpl implements OutbService {

	private transient static final Logger logger = LoggerFactory.getLogger(OutbServiceImpl.class);

	@Autowired
	private OutbMapper mapper;

	@Autowired
	private OutbDtlService outbDtlService;

	@Autowired
	private InbService inbService;

	@Autowired
	private InbDtlService inbDtlService;

	@Autowired
	private WarehouseService warehouseService;

	@Autowired
	private MethodCodeService methodCodeService;

	@Autowired
	private MtdCodeRelaService mtdCodeRelaService;

	@Transactional(readOnly = false)
	@Override
	public Integer save(Outb record) {

		if(record.getId() == null){
			record.setId(IdGeneraterUtils.nextId());
		}

		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
			record.setSsoOrgCode(user.getCorpcode());
			record.setSsoOrgName(user.getOrgName());
			record.setDeptId(user.getDeptId());
			record.setDeptName(user.getDeptname());
		}
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(Outb record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		Outb record = new Outb();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public Outb selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<Outb> getDataSetList(Page page, Outb record) {
		Example example = new Example(Outb.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		List<Outb> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	/**
	 * @param record:
	 * @return void
	 * <AUTHOR>
	 * @description 编辑和删除的准备工作
	 * @date 2025/8/1 16:50
	 */
	@Transactional(readOnly = false)
	public void prepare(OutbInsertReq record) {
		Outb outb = record.getOutb();
		List<OutbDtl> outbDtlList = record.getOutbDtlList();

		// 新增
		if (outb.getId() == null) {
			// 根据方式码进行流水号生成
			if (outb.getMtdCodeId() != null) {
				String flowNo = methodCodeService.genFlowNo(outb.getMtdCodeId());
				outb.setFlowNo(flowNo);
			}
			outb.setId(IdGeneraterUtils.nextId());
			// 默认状态
			outb.setStat(OrdConst.ORD_STAT_REGED);
			outb.setPrintStat(CommonConst.NO);
		}

		// 删除之前的明细
		if (outb.getId() != null) {
			outbDtlService._deleteByOutbId_(outb.getId());
		}

		// 计算总金额
		BigDecimal totalAmt = BigDecimal.ZERO;
		for (OutbDtl outbDtl : outbDtlList) {
			outbDtl.setId(IdGeneraterUtils.nextId());
			outbDtl.setOutbId(outb.getId());
			if (outbDtl.getTotalAmt() != null) {
				totalAmt = totalAmt.add(outbDtl.getTotalAmt());
			}
			outbDtl.setTotalAmt(totalAmt);
		}

		// 设置出库单的总金额
		outb.setTotalAmt(totalAmt);

		// 插入与方式码的关系
		if (outb.getMtdCodeId() != null) {
			MethodCode methodCode = methodCodeService.selectById(outb.getMtdCodeId());
			MtdCodeRela mtdCodeRela = new MtdCodeRela();
			mtdCodeRela.setMtdCodeId(outb.getMtdCodeId());
			mtdCodeRela.setModelType(OrdConst.ORD_TYPE_CK);
			mtdCodeRela.setModelId(outb.getId());
			mtdCodeRela.setMtdCodeJson(JSON.toJSONString(methodCode));
			mtdCodeRelaService.save(mtdCodeRela);
		}
	}

	@Transactional(readOnly = false)
	@Override
	public void insert(OutbInsertReq record) {
		// 准备部分
		prepare(record);

		Outb outb = record.getOutb();
		List<OutbDtl> outbDtlList = record.getOutbDtlList();

		// 写入出库单
		save(outb);

		// 写入出库单详情
		outbDtlService.batchInsert(outbDtlList);
	}

	@Transactional(readOnly = false)
	@Override
	public void edit(OutbInsertReq record) {
		// 准备部分
		prepare(record);

		Outb outb = record.getOutb();
		List<OutbDtl> outbDtlList = record.getOutbDtlList();
		// 写入出库单
		update(outb);
		// 写入出库单详情
		outbDtlService.batchInsert(outbDtlList);
	}

	@Transactional(readOnly = false)
	@Override
	public void remove(String outbId) {
		Assert.hasText(outbId, "出库单ID不能为空.");
		Outb outb = selectById(outbId);

		if (OrdConst.ORD_STAT_CHECKED.equals(outb.getStat())) {
			throw new IllegalArgumentException("已确认的出库单不能删除，请先回滚确认。");
		}

		deleteById(outbId);
		// 删除出库单明细
		outbDtlService.deleteByOutbId(outbId);
		// 删除与方式码的关系
		mtdCodeRelaService.deleteByModelId(OrdConst.ORD_TYPE_CK, outbId);
	}

	@Transactional(readOnly = false)
	@Override
	public void batchRemove(List<String> outbIdList) {
		if (CollectionUtils.isEmpty(outbIdList)) {
			logger.warn("批量删除出库单时，传入的ID列表为空，操作被忽略.");
			return;
		}

		for (String outbId : outbIdList) {
			remove(outbId);
		}
	}

	@Transactional(readOnly = false)
	@Override
	public void batchConfirm(List<String> outbIdList) {
		if (CollectionUtils.isEmpty(outbIdList)) {
			logger.warn("批量确认出库单时，传入的ID列表为空，操作被忽略.");
			return;
		}

		for (String outbId : outbIdList) {
			Outb outb = selectById(outbId);
			if (outb == null) {
				throw new IllegalArgumentException("出库单不存在: " + outbId);
			}

			if (OrdConst.ORD_STAT_CHECKED.equals(outb.getStat())) {
				logger.warn("出库单 {} 已经确认，跳过处理", outbId);
				continue;
			}

			outb.setStat(OrdConst.ORD_STAT_CHECKED);
			update(outb);
		}
	}

	@Transactional(readOnly = false)
	@Override
	public void rollbackBatchConfirm(List<String> outbIdList) {
		if (CollectionUtils.isEmpty(outbIdList)) {
			logger.warn("批量撤销确认出库单时，传入的ID列表为空，操作被忽略.");
			return;
		}

		for (String outbId : outbIdList) {
			Outb outb = selectById(outbId);
			if (outb == null) {
				throw new IllegalArgumentException("出库单不存在: " + outbId);
			}

			if (OrdConst.ORD_STAT_REGED.equals(outb.getStat())) {
				logger.warn("出库单 {} 已经是登记状态，跳过处理", outbId);
				continue;
			}

			outb.setStat(OrdConst.ORD_STAT_REGED);
			update(outb);
		}
	}

	@Override
	public String getTargetOutbId(String currentId, String direction) {
		// 简单实现，实际可能需要根据业务需求调整
		switch (direction) {
			case "current":
				return currentId;
			case "prev":
			case "next":
				// 这里可以实现获取上一条或下一条记录的逻辑
				// 暂时返回当前ID
				return currentId;
			default:
				return currentId;
		}
	}

	@Override
	public OutbInsertReq createFromInb(String inbId) {
		Assert.hasText(inbId, "入库单ID不能为空.");

		// 获取入库单信息
		cn.trasen.ams.material.model.Inb inb = inbService.selectById(inbId);
		if (inb == null) {
			throw new IllegalArgumentException("入库单不存在: " + inbId);
		}

		// 创建出库单
		Outb outb = new Outb();
		outb.setInbId(inbId); // 关联入库单ID
		outb.setWareId(inb.getWhId()); // 使用入库单的仓库
		outb.setOutDeptId(inb.getDeptId()); // 出库科室使用入库科室
		outb.setOutDeptName(inb.getDeptName());

		// 获取入库单明细并转换为出库单明细
		List<OutbDtl> outbDtlList = outbDtlService.createFromInbDtlList(inbId);

		OutbInsertReq outbInsertReq = new OutbInsertReq();
		outbInsertReq.setOutb(outb);
		outbInsertReq.setOutbDtlList(outbDtlList);

		return outbInsertReq;
	}

	@Override
	public void dataFmt(Outb record) {
		if (record == null) {
			return;
		}
		// 可以在这里添加数据格式化逻辑，比如状态显示等
	}
}
