package cn.trasen.ams.material.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import cn.trasen.BootComm.utils.MD5;
import cn.trasen.ams.common.constant.CommonConst;
import cn.trasen.ams.common.model.Warehouse;
import cn.trasen.ams.common.service.WarehouseService;
import cn.trasen.ams.material.bean.outb.OutbInsertReq;
import cn.trasen.ams.material.constant.OrdConst;
import cn.trasen.ams.material.constant.MethodCodeConst;
import cn.trasen.ams.material.model.MethodCode;
import cn.trasen.ams.material.model.MtdCodeRela;
import cn.trasen.ams.material.model.OutbDtl;
import cn.trasen.ams.material.model.StockCur;
import cn.trasen.ams.material.model.StockSeq;
import cn.trasen.ams.material.service.MethodCodeService;
import cn.trasen.ams.material.service.MtdCodeRelaService;
import cn.trasen.ams.material.service.OutbDtlService;
import cn.trasen.ams.material.service.StockCurService;
import cn.trasen.ams.material.service.StockSeqService;
import cn.trasen.ams.common.service.DictService;
import com.alibaba.fastjson.JSON;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.ams.material.dao.OutbMapper;
import cn.trasen.ams.material.model.Outb;
import cn.trasen.ams.material.service.OutbService;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName OutbServiceImpl
 * @Description TODO
 * @date 2025年8月1日 下午4:43:14
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class OutbServiceImpl implements OutbService {

    private transient static final Logger logger = LoggerFactory.getLogger(OutbServiceImpl.class);

    @Autowired
    private OutbMapper mapper;

    @Autowired
    private OutbDtlService outbDtlService;

    @Autowired
    private WarehouseService warehouseService;

    @Autowired
    private MethodCodeService methodCodeService;

    @Autowired
    private MtdCodeRelaService mtdCodeRelaService;

    @Autowired
    private StockCurService stockCurService;

    @Autowired
    private StockSeqService stockSeqService;

    @Autowired
    private DictService dictService;

    @Transactional(readOnly = false)
    @Override
    public Integer save(Outb record) {

        if (record.getId() == null) {
            record.setId(IdGeneraterUtils.nextId());
        }

        record.setCreateDate(new Date());
        record.setUpdateDate(new Date());
        record.setIsDeleted("N");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setCreateUser(user.getUsercode());
            record.setCreateUserName(user.getUsername());
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
            record.setSsoOrgCode(user.getCorpcode());
            record.setSsoOrgName(user.getOrgName());
            record.setDeptId(user.getDeptId());
            record.setDeptName(user.getDeptname());
        }
        return mapper.insertSelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer update(Outb record) {
        record.setUpdateDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer deleteById(String id) {
        Assert.hasText(id, "ID不能为空.");
        Outb record = new Outb();
        record.setId(id);
        record.setUpdateDate(new Date());
        record.setIsDeleted("Y");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public Outb selectById(String id) {
        Assert.hasText(id, "ID不能为空.");
        Outb outb = mapper.selectByPrimaryKey(id);
        dataFmt(outb);
        return outb;
    }

    @Override
    public DataSet<Outb> getDataSetList(Page page, Outb record) {
        Example example = new Example(Outb.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        List<Outb> records = mapper.selectByExampleAndRowBounds(example, page);
        // dataFmt
        if (records != null && !records.isEmpty()) {
            records.forEach(this::dataFmt);
        }
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }

    /**
     * @param record:
     * @return void
     * <AUTHOR>
     * @description 编辑和删除的准备工作
     * @date 2025/8/1 16:50
     */
    @Transactional(readOnly = false)
    public void prepare(OutbInsertReq record) {
        Outb outb = record.getOutb();
        List<OutbDtl> outbDtlList = record.getOutbDtlList();

        // 新增
        if (outb.getId() == null) {
            // 根据方式码进行流水号生成
            if (outb.getMtdCodeId() != null) {
                String flowNo = methodCodeService.genFlowNo(outb.getMtdCodeId());
                outb.setFlowNo(flowNo);
            }
            outb.setId(IdGeneraterUtils.nextId());
            // 默认状态
            outb.setStat(OrdConst.ORD_STAT_REGED);
            outb.setPrintStat(CommonConst.NO);
        }

        // 删除之前的明细
        if (outb.getId() != null) {
            outbDtlService._deleteByOutbId_(outb.getId());
        }

        // 计算总金额
        BigDecimal totalAmt = BigDecimal.ZERO;
        for (OutbDtl outbDtl : outbDtlList) {
            outbDtl.setId(IdGeneraterUtils.nextId());
            outbDtl.setOutbId(outb.getId());
            if (outbDtl.getTotalAmt() != null) {
                totalAmt = totalAmt.add(outbDtl.getTotalAmt());
            }
            outbDtl.setTotalAmt(totalAmt);
        }

        // 设置出库单的总金额
        outb.setTotalAmt(totalAmt);

        // 插入与方式码的关系
        if (outb.getMtdCodeId() != null) {
            MethodCode methodCode = methodCodeService.selectById(outb.getMtdCodeId());
            MtdCodeRela mtdCodeRela = new MtdCodeRela();
            mtdCodeRela.setMtdCodeId(outb.getMtdCodeId());
            mtdCodeRela.setModelType(OrdConst.ORD_TYPE_CK);
            mtdCodeRela.setModelId(outb.getId());
            mtdCodeRela.setMtdCodeJson(JSON.toJSONString(methodCode));
            mtdCodeRelaService.save(mtdCodeRela);
        }
    }

    @Transactional(readOnly = false)
    @Override
    public String insert(OutbInsertReq record) {
        // 准备部分
        prepare(record);

        Outb outb = record.getOutb();
        List<OutbDtl> outbDtlList = record.getOutbDtlList();

        // 写入出库单
        save(outb);

        // 写入出库单详情
        outbDtlService.batchInsert(outbDtlList);
        return outb.getId();
    }

    @Transactional(readOnly = false)
    @Override
    public String edit(OutbInsertReq record) {
        // 准备部分
        prepare(record);

        Outb outb = record.getOutb();
        List<OutbDtl> outbDtlList = record.getOutbDtlList();
        // 写入出库单
        update(outb);
        // 写入出库单详情
        outbDtlService.batchInsert(outbDtlList);
        return outb.getId();
    }

    @Transactional(readOnly = false)
    @Override
    public void remove(String outbId) {
        Assert.hasText(outbId, "出库单ID不能为空.");
        Outb outb = selectById(outbId);

        if (OrdConst.ORD_STAT_CHECKED.equals(outb.getStat())) {
            throw new IllegalArgumentException("已确认的出库单不能删除，请先回滚确认。");
        }

        deleteById(outbId);
        // 删除出库单明细
        outbDtlService.deleteByOutbId(outbId);
        // 删除与方式码的关系
        mtdCodeRelaService.deleteByModelId(OrdConst.ORD_TYPE_CK, outbId);
    }

    @Transactional(readOnly = false)
    @Override
    public void batchRemove(List<String> outbIdList) {
        if (CollectionUtils.isEmpty(outbIdList)) {
            logger.warn("批量删除出库单时，传入的ID列表为空，操作被忽略.");
            return;
        }

        for (String outbId : outbIdList) {
            remove(outbId);
        }
    }

    @Transactional(readOnly = false)
    @Override
    public void confirm(String outbId) {
        Assert.hasText(outbId, "出库单ID不能为空.");

        Outb outb = selectById(outbId);
        if (outb == null) {
            throw new IllegalArgumentException("出库单不存在");
        }

        if (OrdConst.ORD_STAT_CHECKED.equals(outb.getStat())) {
            throw new IllegalArgumentException("出库单已确认，不能重复确认");
        }

        // 获取出库单明细
        List<OutbDtl> outbDtlList = outbDtlService.getOutbDtlListByOutbId(outbId);
        if (CollectionUtils.isEmpty(outbDtlList)) {
            throw new IllegalArgumentException("出库单明细数据缺失，无法完成确认");
        }

        // 获取方式码信息
        MethodCode methodCode = methodCodeService.selectByModelId(OrdConst.ORD_TYPE_CK, outbId);
        if (methodCode == null) {
            throw new IllegalArgumentException("当前出库单没有对应的出库方式，无法完成确认");
        }

        // 构造库存更新数据
        List<StockCur> stockCurList = new ArrayList<>();
        List<StockSeq> stockSeqList = new ArrayList<>();

        outbDtlList.forEach(outbDtl -> {
            StockCur stockCur = new StockCur();
            stockCur.setSkuId(outbDtl.getSkuId());
            stockCur.setWhId(outb.getWareId());
            stockCur.setBatchNo(outbDtl.getBatchNo());

            // 出库操作：减少库存
            if (MethodCodeConst.METHOD_CODE_TYPE_DEC.equals(methodCode.getType())) {
                // 减少库存
                stockCur.setNum(outbDtl.getNum() * -1);
            } else {
                // 增加库存（退库等情况）
                stockCur.setNum(outbDtl.getNum());
            }

            stockCur.setMd5(MD5.string2MD5(outbDtl.getSkuId() + outb.getWareId() + outbDtl.getBatchNo()));
            stockCurList.add(stockCur);

            // 构造库存时序记录
            StockSeq stockSeq = new StockSeq();
            stockSeq.setSkuId(outbDtl.getSkuId());
            stockSeq.setWhId(outb.getWareId());
            stockSeq.setBatchNo(outbDtl.getBatchNo());
            stockSeq.setNum(stockCur.getNum());
            stockSeq.setOrdType(OrdConst.ORD_TYPE_CK);
            stockSeq.setOrdId(outbId);
            stockSeq.setOrdDtlId(outbDtl.getId());
            stockSeqList.add(stockSeq);
        });

        // 更新实时库存
        stockCurService.updateStock(stockCurList);
        // 添加库存时序记录
        stockSeqService.batchInsert(stockSeqList);

        // 更新出库单状态为已确认
        outb.setStat(OrdConst.ORD_STAT_CHECKED);
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            outb.setDoerId(user.getUsercode());
            outb.setDoerName(user.getUsername());
            outb.setCancelerId(null);
            outb.setCancelerName(null);
            outb.setCancelTime(null);
        }
        outb.setDoTime(new Date());
        update(outb);
    }

    @Transactional(readOnly = false)
    @Override
    public void batchConfirm(List<String> outbIdList) {
        if (CollectionUtils.isEmpty(outbIdList)) {
            logger.warn("批量确认出库单时，传入的ID列表为空，操作被忽略.");
            return;
        }

        // 逐个处理每个出库单的确认
        for (String outbId : outbIdList) {
            try {
                confirm(outbId);
            } catch (Exception e) {
                // 记录错误但继续处理其他单据
                throw new RuntimeException("出库单 " + outbId + " 确认失败: " + e.getMessage(), e);
            }
        }
    }

    @Transactional(readOnly = false)
    @Override
    public void rollbackConfirm(String outbId) {
        Assert.hasText(outbId, "出库单ID不能为空.");

        Outb outb = selectById(outbId);
        if (outb == null) {
            throw new IllegalArgumentException("出库单不存在");
        }

        // 检查出库单状态，只有已确认的单据才能回滚
        if (!OrdConst.ORD_STAT_CHECKED.equals(outb.getStat())) {
            throw new IllegalArgumentException("只有已确认的出库单才能进行回滚操作");
        }

        // 获取出库单明细
        List<OutbDtl> outbDtlList = outbDtlService.getOutbDtlListByOutbId(outbId);
        if (CollectionUtils.isEmpty(outbDtlList)) {
            throw new IllegalArgumentException("出库单明细数据缺失，无法完成回滚");
        }

        // 获取方式码信息
        MethodCode methodCode = methodCodeService.selectByModelId(OrdConst.ORD_TYPE_CK, outbId);
        if (methodCode == null) {
            throw new IllegalArgumentException("当前出库单没有对应的出库方式，无法完成回滚");
        }

        // 构造库存回滚数据（与确认时相反的操作）
        List<StockCur> stockCurList = new ArrayList<>();

        outbDtlList.forEach(outbDtl -> {
            StockCur stockCur = new StockCur();
            stockCur.setSkuId(outbDtl.getSkuId());
            stockCur.setWhId(outb.getWareId());
            stockCur.setBatchNo(outbDtl.getBatchNo());

            // 回滚操作：与确认时相反
            if (MethodCodeConst.METHOD_CODE_TYPE_DEC.equals(methodCode.getType())) {
                // 原来是减，现在要加
                stockCur.setNum(outbDtl.getNum());
            } else {
                // 原来是加，现在要减
                stockCur.setNum(outbDtl.getNum() * -1);
            }

            stockCur.setMd5(MD5.string2MD5(outbDtl.getSkuId() + outb.getWareId() + outbDtl.getBatchNo()));
            stockCurList.add(stockCur);
        });

        // 更新实时库存
        stockCurService.updateStock(stockCurList);

        // 更新出库单状态为已登记
        outb.setStat(OrdConst.ORD_STAT_REGED);
        outb.setDoerId(null);
        outb.setDoerName(null);
        outb.setDoTime(null);
        update(outb);
    }

    @Transactional(readOnly = false)
    @Override
    public void rollbackBatchConfirm(List<String> outbIdList) {
        if (CollectionUtils.isEmpty(outbIdList)) {
            logger.warn("批量撤销确认出库单时，传入的ID列表为空，操作被忽略.");
            return;
        }

        // 逐个处理每个出库单的回滚
        for (String outbId : outbIdList) {
            try {
                rollbackConfirm(outbId);
            } catch (Exception e) {
                // 记录错误但继续处理其他单据
                throw new RuntimeException("出库单 " + outbId + " 回滚失败: " + e.getMessage(), e);
            }
        }
    }

    @Override
    public String getTargetOutbId(String currentId, String direction) {
        Assert.hasText(currentId, "当前出库单ID不能为空");
        Assert.hasText(direction, "方向参数不能为空");

        switch (direction.toLowerCase()) {
            case "current":
                // 验证当前ID是否存在
                Outb currentOutb = mapper.selectByPrimaryKey(currentId);
                return (currentOutb != null && !"Y".equals(currentOutb.getIsDeleted())) ? currentId : null;

            case "prev":
                // 这里可以实现获取上一条记录的逻辑
                // 暂时返回当前ID，实际项目中需要在OutbMapper中添加getPrevId方法
                return currentId;

            case "next":
                // 这里可以实现获取下一条记录的逻辑
                // 暂时返回当前ID，实际项目中需要在OutbMapper中添加getNextId方法
                return currentId;

            default:
                throw new IllegalArgumentException("不支持的方向参数: " + direction + "，支持的值: current, prev, next");
        }
    }


    @Override
    public void dataFmt(Outb record) {
        if (record == null) {
            return;
        }
        // 状态显示
        record.setStatShow(dictService.cgetNameByValue(OrdConst.ORD_STAT, record.getStat()));
        // 打印状态显示
        record.setPrintStatShow(dictService.cgetNameByValue(CommonConst.YES_OR_NO, record.getPrintStat()));
    }
}
