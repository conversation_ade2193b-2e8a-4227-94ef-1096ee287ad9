package cn.trasen.ams.material.bean.outb;

import cn.trasen.ams.material.model.Outb;
import cn.trasen.ams.material.model.OutbDtl;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.material.bean.outb
 * @className: OutbInsertReq
 * @author: chenbin
 * @description: 物资出库单创建编辑请求体
 * @date: 2025/8/1 16:30
 * @version: 1.0
 */
@Data
@Validated
public class OutbInsertReq {

    @Valid
    @NotNull(message = "出库单主数据不能为空")
    @ApiModelProperty(value = "出库单主数据")
    private Outb outb;

    @Valid
    @NotNull(message = "出库单明细数据不能为空")
    @ApiModelProperty(value = "出库单明细数据")
    private List<OutbDtl> outbDtlList;
}
