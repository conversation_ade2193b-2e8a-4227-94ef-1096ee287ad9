package cn.trasen.ams.material.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.material.model.Outb;
import cn.trasen.ams.material.service.OutbService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName OutbController
 * @Description TODO
 * @date 2025年8月1日 下午4:43:14
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "OutbController")
public class OutbController {

	private transient static final Logger logger = LoggerFactory.getLogger(OutbController.class);

	@Autowired
	private OutbService outbService;

	/**
	 * @Title saveOutb
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025年8月1日 下午4:43:14
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/material/outb/save")
	public PlatformResult<String> saveOutb(@RequestBody Outb record) {
		try {
			outbService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateOutb
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025年8月1日 下午4:43:14
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/material/outb/update")
	public PlatformResult<String> updateOutb(@RequestBody Outb record) {
		try {
			outbService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectOutbById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<Outb>
	 * @date 2025年8月1日 下午4:43:14
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/material/outb/{id}")
	public PlatformResult<Outb> selectOutbById(@PathVariable String id) {
		try {
			Outb record = outbService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteOutbById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2025年8月1日 下午4:43:14
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/material/outb/delete/{id}")
	public PlatformResult<String> deleteOutbById(@PathVariable String id) {
		try {
			outbService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectOutbList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<Outb>
	 * @date 2025年8月1日 下午4:43:14
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/material/outb/list")
	public DataSet<Outb> selectOutbList(Page page, Outb record) {
		return outbService.getDataSetList(page, record);
	}
}
