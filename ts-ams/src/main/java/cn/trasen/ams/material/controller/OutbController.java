package cn.trasen.ams.material.controller;

import cn.trasen.ams.material.bean.outb.OutbDetailResp;
import cn.trasen.ams.material.bean.outb.OutbDtlResp;
import cn.trasen.ams.material.bean.outb.OutbInsertReq;
import cn.trasen.ams.material.service.OutbDtlService;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.material.model.Outb;
import cn.trasen.ams.material.service.OutbService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
 * @ClassName OutbController
 * @Description TODO
 * @date 2025年8月1日 下午4:43:14
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "OutbController")
public class OutbController {

	private transient static final Logger logger = LoggerFactory.getLogger(OutbController.class);

	@Autowired
	private OutbService outbService;

	@Autowired
	private OutbDtlService outbDtlService;

	/**
	 * @Title saveOutb
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025年8月1日 下午4:43:14
	 * <AUTHOR>
	 */
	@ApiOperation(value = "物资出库单新增", notes = "物资出库单新增")
	@PostMapping("/api/material/outb/save")
	public PlatformResult<String> saveOutb(@Validated @RequestBody OutbInsertReq record) {
		try {
			outbService.insert(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateOutb
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025年8月1日 下午4:43:14
	 * <AUTHOR>
	 */
	@ApiOperation(value = "物资出库单编辑", notes = "物资出库单编辑")
	@PostMapping("/api/material/outb/update")
	public PlatformResult<String> updateOutb(@Validated @RequestBody OutbInsertReq record) {
		try {
			outbService.edit(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 *
	 * @Title selectOutbById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<OutbDetailResp>
	 * @date 2025年8月1日 下午4:43:14
	 * <AUTHOR>
	 */
	@ApiOperation(value = "物资出库单详情", notes = "物资出库单详情")
	@GetMapping("/api/material/outb/{id}")
	public PlatformResult<OutbDetailResp> selectOutbById(
			@PathVariable String id,
			@RequestParam(value = "direction", defaultValue = "current") String direction) {
		try {
			// 根据direction参数获取对应的出库单ID
			String targetId = outbService.getTargetOutbId(id, direction);
			if (targetId == null) {
				return PlatformResult.failure("没有找到" + getDirectionDesc(direction) + "的记录");
			}

			OutbDetailResp record = new OutbDetailResp();
			Outb outb = outbService.selectById(targetId);
			List<OutbDtlResp> outbDtlList = outbDtlService.getOutbDtlExtListByOutbId(outb.getId());

			record.setOutb(outb);
			record.setOutbDtlList(outbDtlList);

			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 获取方向描述
	 * @param direction 方向参数
	 * @return 描述文本
	 */
	private String getDirectionDesc(String direction) {
		switch (direction) {
			case "prev":
				return "上一条";
			case "next":
				return "下一条";
			case "current":
			default:
				return "当前";
		}
	}
	
	
	/**
	 *
	 * @Title deleteOutbById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2025年8月1日 下午4:43:14
	 * <AUTHOR>
	 */
	@ApiOperation(value = "物资出库单删除", notes = "物资出库单删除")
	@PostMapping("/api/material/outb/delete/{id}")
	public PlatformResult<String> deleteOutbById(@PathVariable String id) {
		try {
			outbService.remove(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title batchDeleteOutb
	 * @Description 批量删除出库单
	 * @param outbIdList
	 * @return PlatformResult<String>
	 * @date 2025年8月1日 下午4:50:00
	 * <AUTHOR>
	 */
	@ApiOperation(value = "物资出库单批量删除", notes = "物资出库单批量删除")
	@PostMapping("/api/material/outb/batch/delete")
	public PlatformResult<String> batchDeleteOutb(@RequestBody List<String> outbIdList) {
		try {
			// 参数验证
			if (CollectionUtils.isEmpty(outbIdList)) {
				return PlatformResult.failure("删除的出库单ID列表不能为空");
			}
			// 调用批量删除服务
			outbService.batchRemove(outbIdList);
			return PlatformResult.success("成功删除 " + outbIdList.size() + " 条出库单记录");
		} catch (Exception e) {
			logger.error("批量删除出库单失败: {}", e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectOutbList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<Outb>
	 * @date 2025年8月1日 下午4:43:14
	 * <AUTHOR>
	 */
	@ApiOperation(value = "物资出库单列表", notes = "物资出库单列表")
	@GetMapping("/api/material/outb/list")
	public DataSet<Outb> selectOutbList(Page page, Outb record) {
		return outbService.getDataSetList(page, record);
	}

	/**
	 * @Title batchConfirmOutb
	 * @Description 批量确认出库单
	 * @param outbIdList
	 * @return PlatformResult<String>
	 * @date 2025年8月1日 下午4:50:00
	 * <AUTHOR>
	 */
	@ApiOperation(value = "物资出库单批量确认", notes = "物资出库单批量确认")
	@PostMapping("/api/material/outb/batch/confirm")
	public PlatformResult<String> batchConfirmOutb(@RequestBody List<String> outbIdList) {
		try {
			// 参数验证
			if (CollectionUtils.isEmpty(outbIdList)) {
				return PlatformResult.failure("确认的出库单ID列表不能为空");
			}

			// 调用批量确认服务
			outbService.batchConfirm(outbIdList);
			return PlatformResult.success("成功确认 " + outbIdList.size() + " 条出库单记录");
		} catch (Exception e) {
			logger.error("批量确认出库单失败: {}", e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title rollbackBatchConfirmOutb
	 * @Description 批量撤销确认出库单
	 * @param outbIdList
	 * @return PlatformResult<String>
	 * @date 2025年8月1日 下午4:50:00
	 * <AUTHOR>
	 */
	@ApiOperation(value = "物资出库单批量撤销确认", notes = "物资出库单批量撤销确认")
	@PostMapping("/api/material/outb/batch/rollback-confirm")
	public PlatformResult<String> rollbackBatchConfirmOutb(@RequestBody List<String> outbIdList) {
		try {
			// 参数验证
			if (CollectionUtils.isEmpty(outbIdList)) {
				return PlatformResult.failure("撤销确认的出库单ID列表不能为空");
			}

			// 调用批量撤销确认服务
			outbService.rollbackBatchConfirm(outbIdList);
			return PlatformResult.success("成功撤销确认 " + outbIdList.size() + " 条出库单记录");
		} catch (Exception e) {
			logger.error("批量撤销确认出库单失败: {}", e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title createFromInb
	 * @Description 通过入库单创建出库单
	 * @param inbId
	 * @return PlatformResult<OutbInsertReq>
	 * @date 2025年8月1日 下午4:50:00
	 * <AUTHOR>
	 */
	@ApiOperation(value = "通过入库单创建出库单", notes = "通过入库单创建出库单")
	@GetMapping("/api/material/outb/createFromInb/{inbId}")
	public PlatformResult<OutbInsertReq> createFromInb(@PathVariable String inbId) {
		try {
			OutbInsertReq outbInsertReq = outbService.createFromInb(inbId);
			return PlatformResult.success(outbInsertReq);
		} catch (Exception e) {
			logger.error("通过入库单创建出库单失败: {}", e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
}
