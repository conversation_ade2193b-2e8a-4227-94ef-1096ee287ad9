package cn.trasen.ams.material.service;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.material.model.OutbDtl;

/**
 * @ClassName OutbDtlService
 * @Description TODO
 * @date 2025年8月1日 下午4:43:50
 * <AUTHOR>
 * @version 1.0
 */
public interface OutbDtlService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2025年8月1日 下午4:43:50
	 * <AUTHOR>
	 */
	Integer save(OutbDtl record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2025年8月1日 下午4:43:50
	 * <AUTHOR>
	 */
	Integer update(OutbDtl record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2025年8月1日 下午4:43:50
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return OutbDtl
	 * @date 2025年8月1日 下午4:43:50
	 * <AUTHOR>
	 */
	OutbDtl selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<OutbDtl>
	 * @date 2025年8月1日 下午4:43:50
	 * <AUTHOR>
	 */
	DataSet<OutbDtl> getDataSetList(Page page, OutbDtl record);
}
