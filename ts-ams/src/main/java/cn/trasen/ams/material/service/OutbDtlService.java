package cn.trasen.ams.material.service;

import cn.trasen.ams.material.bean.outb.OutbDtlResp;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.material.model.OutbDtl;

import java.util.List;

/**
 * @ClassName OutbDtlService
 * @Description TODO
 * @date 2025年8月1日 下午4:43:50
 * <AUTHOR>
 * @version 1.0
 */
public interface OutbDtlService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2025年8月1日 下午4:43:50
	 * <AUTHOR>
	 */
	Integer save(OutbDtl record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2025年8月1日 下午4:43:50
	 * <AUTHOR>
	 */
	Integer update(OutbDtl record);

	/**
	 *
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2025年8月1日 下午4:43:50
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return OutbDtl
	 * @date 2025年8月1日 下午4:43:50
	 * <AUTHOR>
	 */
	OutbDtl selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<OutbDtl>
	 * @date 2025年8月1日 下午4:43:50
	 * <AUTHOR>
	 */
	DataSet<OutbDtl> getDataSetList(Page page, OutbDtl record);

	/**
	 * @Title batchInsert
	 * @Description 批量插入出库单明细
	 * @param outbDtlList
	 * @return void
	 * @date 2025年8月1日 下午4:45:00
	 * <AUTHOR>
	 */
	void batchInsert(List<OutbDtl> outbDtlList);

	/**
	 * @Title deleteByOutbId
	 * @Description 根据出库单ID删除明细
	 * @param outbId
	 * @return void
	 * @date 2025年8月1日 下午4:45:00
	 * <AUTHOR>
	 */
	void deleteByOutbId(String outbId);

	/**
	 * @Title _deleteByOutbId_
	 * @Description 根据出库单ID物理删除明细
	 * @param outbId
	 * @return void
	 * @date 2025年8月1日 下午4:45:00
	 * <AUTHOR>
	 */
	void _deleteByOutbId_(String outbId);

	/**
	 * @Title getOutbDtlListByOutbId
	 * @Description 根据出库单ID获取明细列表
	 * @param outbId
	 * @return List<OutbDtl>
	 * @date 2025年8月1日 下午4:45:00
	 * <AUTHOR>
	 */
	List<OutbDtl> getOutbDtlListByOutbId(String outbId);

	/**
	 * @Title getOutbDtlExtListByOutbId
	 * @Description 根据出库单ID获取扩展明细列表
	 * @param outbId
	 * @return List<OutbDtlResp>
	 * @date 2025年8月1日 下午4:45:00
	 * <AUTHOR>
	 */
	List<OutbDtlResp> getOutbDtlExtListByOutbId(String outbId);

	/**
	 * @Title createFromInbDtlList
	 * @Description 通过入库单明细创建出库单明细
	 * @param inbId
	 * @return List<OutbDtl>
	 * @date 2025年8月1日 下午4:45:00
	 * <AUTHOR>
	 */
	List<OutbDtl> createFromInbDtlList(String inbId);

	/**
	 * @Title dataFmt
	 * @Description 数据格式化
	 * @param record
	 * @return void
	 * @date 2025年8月1日 下午4:45:00
	 * <AUTHOR>
	 */
	void dataFmt(OutbDtl record);
}
